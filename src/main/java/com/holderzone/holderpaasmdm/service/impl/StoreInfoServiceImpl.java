package com.holderzone.holderpaasmdm.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.net.url.UrlBuilder;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.Method;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.holderzone.holderpaasmdm.common.constant.RequestConstants;
import com.holderzone.holderpaasmdm.common.constant.UrlPathConstants;
import com.holderzone.holderpaasmdm.converter.GoodsChannelExternalConverter;
import com.holderzone.holderpaasmdm.mapper.service.GoodsChannelExternalMapperService;
import com.holderzone.holderpaasmdm.mapper.service.GoodsPackageSkuMapperService;
import com.holderzone.holderpaasmdm.mapper.service.StoreChannelBindMapperService;
import com.holderzone.holderpaasmdm.mapper.service.StoreGoodsPictureMapperService;
import com.holderzone.holderpaasmdm.model.dto.QueryStoreChannelBindDTO;
import com.holderzone.holderpaasmdm.model.dto.QueryStoreGoodsSkuInventoryDTO;
import com.holderzone.holderpaasmdm.model.dto.QueryStorePosGoodsAvailableForSaleDetailDTO;
import com.holderzone.holderpaasmdm.model.po.GoodsChannelExternalPO;
import com.holderzone.holderpaasmdm.model.po.GoodsPackageSkuPO;
import com.holderzone.holderpaasmdm.model.po.StoreChannelBindPO;
import com.holderzone.holderpaasmdm.model.po.StoreGoodsPicturePO;
import com.holderzone.holderpaasmdm.model.vo.*;
import com.holderzone.holderpaasmdm.service.GoodsCategoryPlatformService;
import com.holderzone.holderpaasmdm.service.StoreGoodsService;
import com.holderzone.holderpaasmdm.service.StoreInfoService;
import com.holderzone.holderpaasmdm.service.UploadFilesService;
import lombok.Cleanup;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Description: 门店信息Service实现类
 *
 * <AUTHOR>
 * @date 2024/12/23
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class StoreInfoServiceImpl implements StoreInfoService {

    private final StoreChannelBindMapperService storeChannelBindMapperService;
    private final StoreGoodsService storeGoodsService;
    private final StoreGoodsPictureMapperService storeGoodsPictureMapperService;
    private final GoodsChannelExternalMapperService goodsChannelExternalMapperService;
    private final GoodsChannelExternalConverter goodsChannelExternalConverter;
    private final UploadFilesService uploadFilesService;
    private final GoodsCategoryPlatformService goodsCategoryPlatformService;
    private final GoodsPackageSkuMapperService goodsPackageSkuMapperService;

    /**
     * 库存服务的内网域名
     */
    @Value("${inventory-service.intranet-host}")
    private String inventoryServiceIntranetHost;

    @Override
    public StoreChannelBindVO queryBindStore(QueryStoreChannelBindDTO queryStoreChannelBindDTO) {
        StoreChannelBindPO channelBind = storeChannelBindMapperService.lambdaQuery()
                .and(item -> {
                    item.eq(StoreChannelBindPO::getBindStoreId, queryStoreChannelBindDTO.getBindStoreId())
                            .or()
                            .eq(StoreChannelBindPO::getStoreId, queryStoreChannelBindDTO.getStoreId());
                })
                .eq(StoreChannelBindPO::getBindType, queryStoreChannelBindDTO.getBindType())
                .eq(StoreChannelBindPO::getChannelId, queryStoreChannelBindDTO.getChannelId())
                .one();
        if (Objects.isNull(channelBind)) {
            throw new IllegalArgumentException("不存在绑定关系");
        }
        StoreChannelBindVO vo = new StoreChannelBindVO();
        BeanUtils.copyProperties(channelBind, vo);
        return vo;
    }

    @Override
    public StoreGoodsExtendChannelVO queryDetails(QueryStorePosGoodsAvailableForSaleDetailDTO queryDTO) {
        // 查询商品和扩展信息
        StoreGoodsExtendVO storeGoodsExtendVO = storeGoodsService.queryStorePosGoodsAvailableForSaleDetail(queryDTO);
        // 新对象复制
        StoreGoodsExtendChannelVO storeGoodsExtendChannelVO = new StoreGoodsExtendChannelVO();
        BeanUtils.copyProperties(storeGoodsExtendVO, storeGoodsExtendChannelVO);
        // 查询渠道扩展信息
        GoodsChannelExternalPO goodsChannelExternalPO = goodsChannelExternalMapperService.findByStoreGoodsId(
                queryDTO.getStoreGoodsId(), queryDTO.getChannelId());
        // 封装扩展信息
        if (Objects.nonNull(goodsChannelExternalPO)) {
            GoodsChannelExternalVO goodsChannelExternalVO = goodsChannelExternalConverter.toGoodsChannelExternalVO(
                    goodsChannelExternalPO);
            // 查询视频资源信息
            if (Objects.nonNull(goodsChannelExternalVO.getVideoId())) {
                goodsChannelExternalVO.setVideo(uploadFilesService.findById(goodsChannelExternalVO.getVideoId()));
            }
            storeGoodsExtendChannelVO.setGoodsChannelExternal(goodsChannelExternalVO);
        }
        // 封装平台类目
        if (Objects.nonNull(storeGoodsExtendVO.getCategoryPlatform())) {
            storeGoodsExtendChannelVO.setCategoryPlatforms(goodsCategoryPlatformService.queryGoodsCategoryPlatform(
                    storeGoodsExtendVO.getCategoryPlatform()));
        }
        // 封装销售分类
        if (CollUtil.isNotEmpty(storeGoodsExtendChannelVO.getCategoryList())) {
            StoreGoodsExtendChannelVO.CategoryLevelVO vo = new StoreGoodsExtendChannelVO.CategoryLevelVO();
            setSaleCategories(vo, storeGoodsExtendChannelVO.getCategoryList());
            storeGoodsExtendChannelVO.setSaleCategories(vo);
        }
        // 查询详情图片数据
        storeGoodsExtendChannelVO.setOtherPictures(findDetailsPicture(queryDTO.getStoreGoodsId()));
        // 查询库存数据
        setSkuInventory(queryDTO, storeGoodsExtendVO.getStoreGoodsSpecDetailsList());
        return storeGoodsExtendChannelVO;
    }

    @Override
    public List<StoreGoodsExtendChannelVO> queryDetailsBatch(QueryStorePosGoodsAvailableForSaleDetailDTO queryDTO) {
        List<StoreGoodsExtendChannelVO> voList = new ArrayList<>();
        for (Integer storeGoodsId : queryDTO.getStoreGoodsIds()) {
            queryDTO.setStoreGoodsId(storeGoodsId);
            try {
                voList.add(queryDetails(queryDTO));
            } catch (Exception e) {
                log.error("批量查询数据，未查询到商品信息, storeGoodsId -> {}", storeGoodsId);
            }
        }
        //todo


        return voList;
    }

    @Override
    public List<StoreGoodsSkuInventoryVO> querySkuInventory(QueryStoreGoodsSkuInventoryDTO inventoryDTO) {
        // 查询sku数据信息
        List<GoodsPackageSkuPO> goodsPackageSkuPOList = goodsPackageSkuMapperService.findByStoreGoodsIdInAndStoreId(
                inventoryDTO.getStoreGoodsIds(), inventoryDTO.getStoreId());
        if (goodsPackageSkuPOList.isEmpty()) {
            return List.of();
        }
        List<String> skuCodeList = goodsPackageSkuPOList.stream().map(GoodsPackageSkuPO::getSkuCode).toList();
        // 查询库存数据
        List<SkuInventory> inventoryList = requestInventory(skuCodeList, inventoryDTO.getChannelId(),
                inventoryDTO.getStoreId(), inventoryDTO.getCompanyId());
        // 遍历数据
        List<StoreGoodsSkuInventoryVO> voList = new ArrayList<>();
        for (GoodsPackageSkuPO goodsPackageSkuPO : goodsPackageSkuPOList) {
            StoreGoodsSkuInventoryVO inventoryVO = new StoreGoodsSkuInventoryVO();
            inventoryVO.setStoreGoodsId(goodsPackageSkuPO.getStoreGoodsId());
            inventoryVO.setSkuCode(goodsPackageSkuPO.getSkuCode());
            inventoryVO.setSkuId(goodsPackageSkuPO.getId());
            // 获取库存
            inventoryVO.setInventory(getSkuInventory(inventoryList, goodsPackageSkuPO.getSkuCode()));
            voList.add(inventoryVO);
        }
        return voList;
    }

    @Override
    public Integer countByFreightTemplateId(Long freightTemplateId) {
        Long count = goodsChannelExternalMapperService.lambdaQuery()
                .eq(GoodsChannelExternalPO::getFreightTemplateId, freightTemplateId)
                .count();
        return count.intValue();
    }

    /**
     * 设置库存数据
     *
     * @param queryDTO    查询对象
     * @param detailsList 列表详情对象
     */
    private void setSkuInventory(QueryStorePosGoodsAvailableForSaleDetailDTO queryDTO,
                                 List<StoreGoodsSpecAndDetailsFullVO> detailsList) {
        if (!Boolean.TRUE.equals(queryDTO.getIsQueryInventory()) || CollUtil.isEmpty(detailsList)) {
            return;
        }
        // 封装请求参数
        List<String> skuCodeList = detailsList.stream().map(StoreGoodsSpecAndDetailsFullVO::getSkuCode).toList();
        // 请求接口，获取库存
        List<SkuInventory> skuInventoryList = requestInventory(skuCodeList, queryDTO.getChannelId(),
                queryDTO.getStoreId(), queryDTO.getCompanyId());
        // 设置库存数据
        for (StoreGoodsSpecAndDetailsFullVO fullVO : detailsList) {
            // 获取库存
            fullVO.setInventory(getSkuInventory(skuInventoryList, fullVO.getSkuCode()));
        }
    }

    /**
     * 获取sku库存数据
     *
     * @param skuInventoryList 库存集合
     * @param skuCode          sku码
     * @return 库存
     */
    private Integer getSkuInventory(List<SkuInventory> skuInventoryList, String skuCode) {
        Optional<SkuInventory> optional = skuInventoryList.stream()
                .filter(item -> Objects.equals(item.getSkuCode(), skuCode))
                .findFirst();
        if (optional.isPresent()) {
            return optional.get().getQuantity();
        }
        return 0;
    }

    /**
     * 递归查询销售分类
     *
     * @param vo           vo
     * @param categoryList 分类列表
     */
    private void setSaleCategories(StoreGoodsExtendChannelVO.CategoryLevelVO vo,
                                   List<StoreGoodsBaseExtendVO.SaleCategoryVO> categoryList) {
        for (StoreGoodsBaseExtendVO.SaleCategoryVO saleCategoryVO : categoryList) {
            if (saleCategoryVO.getLevel() == 1) {
                vo.setOne(new StoreGoodsExtendChannelVO.IdNameVO(saleCategoryVO.getId(), saleCategoryVO.getName()));
            } else if (saleCategoryVO.getLevel() == 2) {
                vo.setTwo(new StoreGoodsExtendChannelVO.IdNameVO(saleCategoryVO.getId(), saleCategoryVO.getName()));
            } else if (saleCategoryVO.getLevel() == 3) {
                vo.setThree(new StoreGoodsExtendChannelVO.IdNameVO(saleCategoryVO.getId(), saleCategoryVO.getName()));
            }
            if (CollUtil.isNotEmpty(saleCategoryVO.getChildList())) {
                setSaleCategories(vo, saleCategoryVO.getChildList());
            }
        }
    }

    /**
     * 请求获取库存数据
     *
     * @param skuCodeList sku唯一码
     * @param channelId   渠道id
     * @param storeId     店铺id
     * @param companyId   公司id
     */
    private List<SkuInventory> requestInventory(List<String> skuCodeList, Integer channelId, Integer storeId,
                                                Long companyId) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.set("channel_id", channelId);
        jsonObject.set("store_id", storeId);
        jsonObject.set("sku_code_list", skuCodeList);
        // 请求
        String url = inventoryServiceIntranetHost + UrlPathConstants.SELECT_CHANNEL_INVENTORY;
        HttpRequest httpRequest = new HttpRequest(UrlBuilder.of(url));
        httpRequest.header(RequestConstants.COMPANY_ID_HEADER_KEY, String.valueOf(companyId))
                .body(jsonObject.toString())
                .method(Method.POST);
        try {
            @Cleanup HttpResponse execute = httpRequest.execute();
            if (execute.getStatus() == 200) {
                jsonObject.clear();
                String body = execute.body();
                log.info("请求sku库存接口, 请求 -> {}, 响应 -> {}", jsonObject, body);
                jsonObject = JSONUtil.parseObj(body);
                if (jsonObject.getInt("code") == 0) {
                    return JSONUtil.toList(jsonObject.getStr("data"), SkuInventory.class);
                } else {
                    log.error("请求sku库存接口错误, {}", jsonObject.getStr("msg"));
                }
            } else {
                log.error("请求sku库存接口失败, {}", execute.body());
            }
        } catch (Exception e) {
            log.error("请求sku库存接口抛出错误", e);
        }
        return List.of();
    }

    /**
     * 商品id查询详情图片信息
     *
     * @param goodsId 商品id
     * @return 查询结果
     */
    private Map<Integer, List<GoodsPictureVO>> findDetailsPicture(Integer goodsId) {
        // 查询详情图片数据
        List<StoreGoodsPicturePO> picturePOList = storeGoodsPictureMapperService.lambdaQuery()
                .eq(StoreGoodsPicturePO::getGoodsId, goodsId)
                .list();
        if (picturePOList.isEmpty()) {
            return null;
        }
        // 查询图片数据
        List<GoodsPictureVO> goodsPictureVOS = uploadFilesService.findByIdIn(picturePOList.stream()
                .map(StoreGoodsPicturePO::getImageId)
                .toList());
        picturePOList.forEach(item -> {
            if (Objects.isNull(item.getType())) {
                item.setType(0);
            }
        });
        // 根据类型分组
        Map<Integer, List<StoreGoodsPicturePO>> listMap = picturePOList.stream()
                .collect(Collectors.groupingBy(StoreGoodsPicturePO::getType));
        Map<Integer, List<GoodsPictureVO>> returnMap = new HashMap<>();
        for (Map.Entry<Integer, List<StoreGoodsPicturePO>> entry : listMap.entrySet()) {
            List<Integer> ids = entry.getValue().stream().map(StoreGoodsPicturePO::getImageId).toList();
            if (!ids.isEmpty()) {
                returnMap.put(entry.getKey(), goodsPictureVOS.stream()
                        .filter(item -> ids.contains(item.getId()))
                        .collect(Collectors.toList()));
            }
        }
        return returnMap;

    }

    @Data
    public static class SkuInventory {

        /**
         * 数量
         */
        private Integer quantity;

        /**
         * sku标识
         */
        private String skuCode;
    }
}